import yaml
import json
from pathlib import Path
def yaml_to_json(yaml_file, json_file=None):
    """
    将YAML文件转换为JSON格式
    
    参数:
        yaml_file (str): 输入的YAML文件路径
        json_file (str, 可选): 输出的JSON文件路径。如果未提供，则使用与YAML文件相同的路径，仅修改扩展名
    """
    # 读取YAML文件
    with open(yaml_file, 'r', encoding='utf-8') as f:
        yaml_data = yaml.safe_load(f)
    
    # 确定输出JSON文件路径
    if json_file is None:
        yaml_path = Path(yaml_file)
        json_file = yaml_path.with_suffix('.json')
    
    # 写入JSON文件
    with open(json_file, 'w', encoding='utf-8') as f:
        json.dump(yaml_data, f, indent=2, ensure_ascii=False)
    
    print(f"转换完成，JSON文件已保存到: {json_file}")

output_yaml = '/home/<USER>/panpan/code/Calib/camera_calib-main_org/config_0829/camera_intrinsics_1.yaml'
json_file = '/home/<USER>/panpan/code/Calib/camera_calib-main_org/config_0829/camera_intrinsics_1.json'

yaml_to_json(output_yaml, json_file)