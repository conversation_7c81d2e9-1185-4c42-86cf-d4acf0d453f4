version: 1.0.0
camera_intrinsics:
- - 413.27741293606965
  - 0.0
  - 403.3678575640962
- - 0.0
  - 413.16503014585834
  - 298.6378302939596
- - 0.0
  - 0.0
  - 1.0
distortion_coefficients:
- - -0.11235544757726962
  - 0.050806540635559026
  - 0.0
  - 0.0
  - -0.02781710933374134
reprojection_error: 0.025572524165422668
camera_parameters:
  intrinsics_and_distortion:
  - 413.27741293606965
  - 413.16503014585834
  - 403.3678575640962
  - 298.6378302939596
  - -0.11235544757726962
  - 0.050806540635559026
  - 0.0
  - 0.0
  - -0.02781710933374134
  - 0.0
  - 0.0
  - 0.0
  camera_matrix:
    fx: 413.27741293606965
    fy: 413.16503014585834
    cx: 403.3678575640962
    cy: 298.6378302939596
  distortion_coefficients:
    radial:
      k1: -0.11235544757726962
      k2: 0.050806540635559026
      k3: -0.02781710933374134
      k4: 0.0
      k5: 0.0
      k6: 0.0
    tangential:
      p1: 0.0
      p2: 0.0
image_properties:
  width: 800
  height: 600
  channels: 1
  pixel_format: gray
calibration_quality:
  mean_reprojection_error: 0.025572524165422668
  calibration_date: '2025-08-29'
  calibration_method: chessboard
  num_calibration_images: 17
chessboard_info:
  board_size:
  - 11
  - 8
camera_parameters_extrinsics:
  image_1:
    translation_vector: '[[-124.12966271  -98.6574639   293.46848883]]

      '
    rotation_matrix: "[[ 0.99742016  0.05208463 -0.04939864]\n [-0.05852674  0.98849277\
      \ -0.13948712]\n [ 0.04156506  0.14201841  0.98899096]]\n"
  image_2:
    translation_vector: '[[ -87.57357301 -238.73381348  394.7184563 ]]

      '
    rotation_matrix: "[[ 9.96034047e-01 -8.89712912e-02  5.34862925e-04]\n [ 8.80274328e-02\
      \  9.84557883e-01 -1.51317369e-01]\n [ 1.29362982e-02  1.50764334e-01  9.88485087e-01]]\n"
  image_3:
    translation_vector: '[[-252.66592098 -235.11273246  398.39330627]]

      '
    rotation_matrix: "[[ 0.98182176 -0.0737322   0.1748988 ]\n [ 0.11440087  0.96516069\
      \ -0.23532377]\n [-0.15145451  0.25105458  0.9560508 ]]\n"
  image_4:
    translation_vector: '[[-336.92301643 -198.54613835  355.38761528]]

      '
    rotation_matrix: "[[ 0.98876225 -0.02832751 -0.14678816]\n [ 0.01791492  0.99725967\
      \ -0.07177883]\n [ 0.14841923  0.0683425   0.9865602 ]]\n"
  image_5:
    translation_vector: '[[-334.1821658   -42.40098163  350.98463195]]

      '
    rotation_matrix: "[[ 0.98363135  0.0198218  -0.17909902]\n [-0.03035312  0.997955\
      \   -0.05625388]\n [ 0.17761771  0.0607693   0.98222148]]\n"
  image_6:
    translation_vector: '[[-381.6523444   100.66562951  402.50270856]]

      '
    rotation_matrix: "[[ 0.98108024  0.02720268 -0.19168093]\n [-0.02979464  0.99949928\
      \ -0.01065245]\n [ 0.19129518  0.01616198  0.98139948]]\n"
  image_7:
    translation_vector: '[[-208.29147863  121.21821418  482.96446584]]

      '
    rotation_matrix: "[[ 0.97128813  0.08066958 -0.22381196]\n [-0.0376481   0.98102117\
      \  0.19021062]\n [ 0.23490848 -0.17632322  0.95589127]]\n"
  image_8:
    translation_vector: '[[-199.17655584  -33.25179339  457.67992801]]

      '
    rotation_matrix: "[[ 0.98164321  0.03999059 -0.1864869 ]\n [-0.04834092  0.9980117\
      \  -0.04044501]\n [ 0.18449869  0.04871752  0.98162459]]\n"
  image_9:
    translation_vector: '[[  32.58535417 -107.99632017  553.33397611]]

      '
    rotation_matrix: "[[ 0.99873369 -0.04475222 -0.02298373]\n [ 0.04313967  0.99685867\
      \ -0.06642107]\n [ 0.02588402  0.06534545  0.99752694]]\n"
  image_10:
    translation_vector: '[[ 110.80323788 -244.91848015  483.87077692]]

      '
    rotation_matrix: "[[ 0.99624834  0.01826337  0.08459132]\n [ 0.0296162   0.8465045\
      \  -0.53155715]\n [-0.08131496  0.5320682   0.84278782]]\n"
  image_11:
    translation_vector: '[[ 200.94413926 -249.13236688  527.44039927]]

      '
    rotation_matrix: "[[ 0.94914563 -0.00768292 -0.31474361]\n [-0.08968161  0.95168881\
      \ -0.29367604]\n [ 0.30179426  0.30696805  0.90260226]]\n"
  image_12:
    translation_vector: '[[147.06312172 -97.37683902 545.76628457]]

      '
    rotation_matrix: "[[ 0.85111401  0.22584029  0.47392099]\n [-0.12542659  0.96407284\
      \ -0.23416174]\n [-0.50977751  0.13985604  0.84886228]]\n"
  image_13:
    translation_vector: '[[ 86.63321613 110.05449672 555.22788984]]

      '
    rotation_matrix: "[[ 0.92808252  0.12115564  0.35211383]\n [-0.16855412  0.97985487\
      \  0.10711646]\n [-0.33204269 -0.15876315  0.92980746]]\n"
  image_14:
    translation_vector: '[[ 49.97262428 153.9142853  539.12247188]]

      '
    rotation_matrix: "[[ 0.91881184  0.10942606  0.37922387]\n [-0.04199068  0.98244786\
      \ -0.18174979]\n [-0.39245584  0.15106999  0.9072796 ]]\n"
  image_15:
    translation_vector: '[[ 99.95656125 -67.30108249 430.81017975]]

      '
    rotation_matrix: "[[ 0.96654486  0.25638647 -0.00755089]\n [-0.2109966   0.77800235\
      \ -0.59177089]\n [-0.14584744  0.57356632  0.80607084]]\n"
  image_16:
    translation_vector: '[[-399.07097203 -131.95867642  470.42653003]]

      '
    rotation_matrix: "[[ 0.99967676  0.01484417  0.02064028]\n [-0.00899776  0.96587519\
      \ -0.25885161]\n [-0.02377837  0.25858223  0.96569655]]\n"
  image_17:
    translation_vector: '[[-387.3334331  -111.85455507  406.27223727]]

      '
    rotation_matrix: "[[ 0.9674583   0.0942728  -0.23481285]\n [-0.1617844   0.94399447\
      \ -0.2875765 ]\n [ 0.19455139  0.31620733  0.92852716]]\n"
