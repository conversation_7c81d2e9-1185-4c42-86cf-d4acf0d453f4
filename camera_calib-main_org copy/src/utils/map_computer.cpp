//
// Created by rang<PERSON><PERSON> on 19-6-18.
//

#include "utils/map_computer.h"
#include <iostream>
#include <opencv2/opencv.hpp>
#include <opencv2/imgproc.hpp>
#include <opencv2/calib3d.hpp>

using namespace cv;
using namespace std;

// 添加自定义的computeTiltProjectionMatrix实现
void computeTiltProjectionMatrix(double tauX, double tauY, cv::Matx33d* matTilt) {
    // 根据倾斜角度计算投影矩阵
    double tiltXY = std::sqrt(tauX*tauX + tauY*tauY);
    if (tiltXY < 1e-6) {
        *matTilt = cv::Matx33d::eye();
        return;
    }
    
    double sinPhi = tauY / tiltXY;
    double cosPhi = tauX / tiltXY;
    double theta = std::atan(tiltXY);
    double cosTheta = std::cos(theta);
    double sinTheta = std::sin(theta);
    
    cv::Matx33d R1 = cv::Matx33d(
        cosPhi, -sin<PERSON>hi, 0,
        sin<PERSON>hi, cosPhi, 0,
        0, 0, 1
    );
    
    cv::Matx33d R2 = cv::Matx33d(
        1, 0, 0,
        0, cosTheta, -sinTheta,
        0, sinTheta, cosTheta
    );
    
    cv::Matx33d R3 = cv::Matx33d(
        cosPhi, sinPhi, 0,
        -sinPhi, cosPhi, 0,
        0, 0, 1
    );
    
    *matTilt = R3 * R2 * R1;
}

class initUndistortRectifyMapComputer : public cv::ParallelLoopBody
{
public:
    initUndistortRectifyMapComputer(
            cv::Size _size, cv::Mat &_map1, cv::Mat &_map2, int _m1type,
            const double* _ir, cv::Matx33d &_matTilt,
            double _u0, double _v0, double _fx, double _fy,
            double _k1, double _k2, double _p1, double _p2,
            double _k3, double _k4, double _k5, double _k6,
            double _s1, double _s2, double _s3, double _s4,
            double _h00, double _h01, double _h02, double _h10,
            double _h11, double _h12, double _h20, double _h21, double _h22)
            : size(_size),
              map1(_map1),
              map2(_map2),
              m1type(_m1type),
              ir(_ir),
              matTilt(_matTilt),
              u0(_u0),
              v0(_v0),
              fx(_fx),
              fy(_fy),
              k1(_k1),
              k2(_k2),
              p1(_p1),
              p2(_p2),
              k3(_k3),
              k4(_k4),
              k5(_k5),
              k6(_k6),
              s1(_s1),
              s2(_s2),
              s3(_s3),
              s4(_s4),
              h00(_h00),
              h01(_h01),
              h02(_h02),
              h10(_h10),
              h11(_h11),
              h12(_h12),
              h20(_h20),
              h21(_h21),
              h22(_h22)
    {}

    void operator()( const cv::Range& range ) const CV_OVERRIDE
    {
        const int begin = range.start;
        const int end = range.end;

        for( int i = begin; i < end; i++ )
        {
            float* m1f = map1.ptr<float>(i);
            float* m2f = map2.empty() ? 0 : map2.ptr<float>(i);
            short* m1 = (short*)m1f;
            ushort* m2 = (ushort*)m2f;
            double _x = 0, _y = 0, _w = 0;
            int j = 0;

            if (m1type == CV_16SC2)
                CV_Assert(m1 != NULL && m2 != NULL);
            else if (m1type == CV_32FC1)
                CV_Assert(m1f != NULL && m2f != NULL);
            else
                CV_Assert(m1 != NULL);


            for( ; j < size.width; j++ )
            {
                //乘单应矩阵的逆
                double jj = (h00*j + h01*i + h02)/(h20*j + h21*i + h22);
                double ii = (h10*j + h11*i + h12)/(h20*j + h21*i + h22);
                //乘内参矩阵的逆，变换到相机坐标系
                _x = ir[0]*jj + ii*ir[1] + ir[2];
                _y = ir[3]*jj + ii*ir[4] + ir[5];
                _w = ir[6]*jj + ii*ir[7] + ir[8];

                double w = 1./_w, x = _x*w, y = _y*w;
                double x2 = x*x, y2 = y*y;
                double r2 = x2 + y2, _2xy = 2*x*y;
                double kr = (1 + ((k3*r2 + k2)*r2 + k1)*r2)/(1 + ((k6*r2 + k5)*r2 + k4)*r2);
                double xd = (x*kr + p1*_2xy + p2*(r2 + 2*x2) + s1*r2+s2*r2*r2);
                double yd = (y*kr + p1*(r2 + 2*y2) + p2*_2xy + s3*r2+s4*r2*r2);
                cv::Vec3d vecTilt = matTilt*cv::Vec3d(xd, yd, 1);
                double invProj = vecTilt(2) ? 1./vecTilt(2) : 1;
                double u = fx*invProj*vecTilt(0) + u0;
                double v = fy*invProj*vecTilt(1) + v0;


                if( m1type == CV_16SC2 )
                {
                    int iu = cv::saturate_cast<int>(u*cv::INTER_TAB_SIZE);
                    int iv = cv::saturate_cast<int>(v*cv::INTER_TAB_SIZE);
                    m1[j*2] = (short)(iu >> cv::INTER_BITS);
                    m1[j*2+1] = (short)(iv >> cv::INTER_BITS);
                    m2[j] = (ushort)((iv & (cv::INTER_TAB_SIZE-1))*cv::INTER_TAB_SIZE + (iu & (cv::INTER_TAB_SIZE-1)));
                }
                else if( m1type == CV_32FC1 )
                {
                    m1f[j] = (float)u;
                    m2f[j] = (float)v;
                }
                else
                {
                    m1f[j*2] = (float)u;
                    m1f[j*2+1] = (float)v;
                }
            }
        }
    }

private:
    cv::Size size;
    cv::Mat &map1;
    cv::Mat &map2;
    int m1type;
    const double* ir;
    cv::Matx33d &matTilt;
    double u0;
    double v0;
    double fx;
    double fy;
    double k1;
    double k2;
    double p1;
    double p2;
    double k3;
    double k4;
    double k5;
    double k6;
    double s1;
    double s2;
    double s3;
    double s4;
    double h00;
    double h01;
    double h02;
    double h10;
    double h11;
    double h12;
    double h20;
    double h21;
    double h22;

#if CV_TRY_AVX2
    bool useAVX2;
#endif
};

void initCalibRectifyMap( cv::InputArray _cameraMatrix, cv::InputArray _distCoeffs, cv::InputArray _homoIMatrix,
                          cv::InputArray _matR, cv::InputArray _newCameraMatrix,
                          cv::Size size, int m1type, cv::OutputArray _map1, cv::OutputArray _map2 )
{
    // cout<<"Start Calculate Mapx&Mapy......"<<endl;
    Mat cameraMatrix = _cameraMatrix.getMat(), distCoeffs = _distCoeffs.getMat(), homoIMatrix = _homoIMatrix.getMat();
    Mat matR = _matR.getMat(), newCameraMatrix = _newCameraMatrix.getMat();

    if( m1type <= 0 )
        m1type = CV_16SC2;
    CV_Assert( m1type == CV_16SC2 || m1type == CV_32FC1 || m1type == CV_32FC2 );
    _map1.create( size, m1type );
    Mat map1 = _map1.getMat(), map2;
    if( m1type != CV_32FC2 )
    {
        _map2.create( size, m1type == CV_16SC2 ? CV_16UC1 : CV_32FC1 );
        map2 = _map2.getMat();
    }
    else
        _map2.release();

    Mat_<double> R = Mat_<double>::eye(3, 3);
    Mat_<double> A = Mat_<double>(cameraMatrix), Ar;

    if( !newCameraMatrix.empty() )
        Ar = Mat_<double>(newCameraMatrix);
    else
        Ar = getDefaultNewCameraMatrix( A, size, true );

    if( !matR.empty() )
        R = Mat_<double>(matR);

    if( !distCoeffs.empty() )
        distCoeffs = Mat_<double>(distCoeffs);
    else
    {
        distCoeffs.create(14, 1, CV_64F);
        distCoeffs = 0.;
    }

    CV_Assert( A.size() == Size(3,3) && A.size() == R.size() );
    CV_Assert( Ar.size() == Size(3,3) || Ar.size() == Size(4, 3));
    Mat_<double> iR = (Ar.colRange(0,3)*R).inv(DECOMP_LU);
    const double* ir = &iR(0,0);

    double u0 = A(0, 2),  v0 = A(1, 2);
    double fx = A(0, 0),  fy = A(1, 1);

    CV_Assert( distCoeffs.size() == Size(1, 4) || distCoeffs.size() == Size(4, 1) ||
               distCoeffs.size() == Size(1, 5) || distCoeffs.size() == Size(5, 1) ||
               distCoeffs.size() == Size(1, 8) || distCoeffs.size() == Size(8, 1) ||
               distCoeffs.size() == Size(1, 12) || distCoeffs.size() == Size(12, 1) ||
               distCoeffs.size() == Size(1, 14) || distCoeffs.size() == Size(14, 1));

    if( distCoeffs.rows != 1 && !distCoeffs.isContinuous() )
        distCoeffs = distCoeffs.t();

    const double* const distPtr = distCoeffs.ptr<double>();
    double k1 = distPtr[0];
    double k2 = distPtr[1];
    double p1 = distPtr[2];
    double p2 = distPtr[3];
    double k3 = distCoeffs.cols + distCoeffs.rows - 1 >= 5 ? distPtr[4] : 0.;
    double k4 = distCoeffs.cols + distCoeffs.rows - 1 >= 8 ? distPtr[5] : 0.;
    double k5 = distCoeffs.cols + distCoeffs.rows - 1 >= 8 ? distPtr[6] : 0.;
    double k6 = distCoeffs.cols + distCoeffs.rows - 1 >= 8 ? distPtr[7] : 0.;
    double s1 = distCoeffs.cols + distCoeffs.rows - 1 >= 12 ? distPtr[8] : 0.;
    double s2 = distCoeffs.cols + distCoeffs.rows - 1 >= 12 ? distPtr[9] : 0.;
    double s3 = distCoeffs.cols + distCoeffs.rows - 1 >= 12 ? distPtr[10] : 0.;
    double s4 = distCoeffs.cols + distCoeffs.rows - 1 >= 12 ? distPtr[11] : 0.;
    double tauX = distCoeffs.cols + distCoeffs.rows - 1 >= 14 ? distPtr[12] : 0.;
    double tauY = distCoeffs.cols + distCoeffs.rows - 1 >= 14 ? distPtr[13] : 0.;

    Mat_<double> HI = Mat_<double>(homoIMatrix), Hr;
    double h00 =HI(0,0), h01 =HI(0,1), h02 =HI(0,2);
    double h10 =HI(1,0), h11 =HI(1,1), h12 =HI(1,2);
    double h20 =HI(2,0), h21 =HI(2,1), h22 =HI(2,2);

    // Matrix for trapezoidal distortion of tilted image sensor
    cv::Matx33d matTilt = cv::Matx33d::eye();
    computeTiltProjectionMatrix(tauX, tauY, &matTilt);

    parallel_for_(Range(0, size.height), initUndistortRectifyMapComputer(
            size, map1, map2, m1type, ir, matTilt, u0, v0,
            fx, fy, k1, k2, p1, p2, k3, k4, k5, k6, s1, s2, s3, s4,
            h00, h01, h02, h10, h11, h12, h20, h21, h22));

}
