from PIL import Image

def resize_image(input_path, output_path, width=None, height=None, scale=None):
    """
    调整图片尺寸
    :param input_path: 输入图片路径
    :param output_path: 输出图片路径
    :param width: 目标宽度（可选）
    :param height: 目标高度（可选）
    :param scale: 缩放比例（可选，例如0.5表示缩小一半）
    """
    # 打开原始图片
    original_image = Image.open(input_path)
    
    # 获取原始尺寸
    original_width, original_height = original_image.size
    
    # 计算目标尺寸
    if scale is not None:
        # 按比例缩放
        new_width = int(original_width * scale)
        new_height = int(original_height * scale)
    elif width is not None and height is not None:
        # 指定宽高
        new_width, new_height = width, height
    elif width is not None:
        # 只指定宽度，高度按比例计算
        ratio = width / original_width
        new_height = int(original_height * ratio)
        new_width = width
    elif height is not None:
        # 只指定高度，宽度按比例计算
        ratio = height / original_height
        new_width = int(original_width * ratio)
        new_height = height
    else:
        raise ValueError("必须指定width、height或scale中的至少一个参数")
    
    # 调整图片尺寸
    resized_image = original_image.resize((new_width, new_height), Image.Resampling.LANCZOS)
    
    # 保存调整后的图片
    resized_image.save(output_path)
    print(f"图片已调整尺寸并保存到 {output_path}")
    print(f"原始尺寸: {original_width}x{original_height}")
    print(f"新尺寸: {new_width}x{new_height}")

# 使用示例
if __name__ == "__main__":
    # # 示例1：按比例缩放
    # resize_image("input.jpg", "output_scaled.jpg", scale=0.5)
    
    # # 示例2：指定宽度，高度自动按比例调整
    # resize_image("input.jpg", "output_width.jpg", width=300)
    
    # # 示例3：指定高度，宽度自动按比例调整
    # resize_image("input.jpg", "output_height.jpg", height=200)
    
    # 示例4：同时指定宽度和高度（可能变形）
    resize_image("/home/<USER>/panpan/code/Calib/camera_calib-main_org/data/input/0723/20250723-111146.bmp", "/home/<USER>/panpan/code/Calib/camera_calib-main_org/data/input/0723/20250723-111146_resize.bmp", 
                 scale=0.5)