#ifndef CAMERA_CALIBRATION_UTILS_FILE_UTILS_H
#define CAMERA_CALIBRATION_UTILS_FILE_UTILS_H

#include <string>

namespace camera_calibration {
namespace utils {

/**
 * @brief 文件工具类
 * 
 * 提供跨平台的文件系统操作功能，兼容不同的 C++ 标准
 */
class FileUtils {
public:
    /**
     * @brief 检查文件或目录是否存在
     * @param path 文件或目录路径
     * @return 存在返回 true，否则返回 false
     */
    static bool exists(const std::string& path);
    
    /**
     * @brief 创建目录（包括父目录）
     * @param path 目录路径
     * @return 创建成功返回 true，否则返回 false
     */
    static bool createDirectories(const std::string& path);
    
    /**
     * @brief 检查路径是否为目录
     * @param path 路径
     * @return 是目录返回 true，否则返回 false
     */
    static bool isDirectory(const std::string& path);
    
    /**
     * @brief 检查路径是否为文件
     * @param path 路径
     * @return 是文件返回 true，否则返回 false
     */
    static bool isFile(const std::string& path);
    
    /**
     * @brief 获取文件大小
     * @param path 文件路径
     * @return 文件大小（字节），失败返回 -1
     */
    static long getFileSize(const std::string& path);
    
    /**
     * @brief 获取文件的目录部分
     * @param path 文件路径
     * @return 目录路径
     */
    static std::string getDirectory(const std::string& path);
    
    /**
     * @brief 获取文件名部分（不包含路径）
     * @param path 文件路径
     * @return 文件名
     */
    static std::string getFileName(const std::string& path);
    
    /**
     * @brief 获取文件扩展名
     * @param path 文件路径
     * @return 扩展名（包含点号）
     */
    static std::string getExtension(const std::string& path);
    
    /**
     * @brief 连接路径
     * @param path1 第一个路径
     * @param path2 第二个路径
     * @return 连接后的路径
     */
    static std::string joinPath(const std::string& path1, const std::string& path2);
    
    /**
     * @brief 规范化路径（处理 . 和 .. 等）
     * @param path 输入路径
     * @return 规范化后的路径
     */
    static std::string normalizePath(const std::string& path);
};

} // namespace utils
} // namespace camera_calibration

#endif // CAMERA_CALIBRATION_UTILS_FILE_UTILS_H
