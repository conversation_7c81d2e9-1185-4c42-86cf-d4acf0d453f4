/**
 * @file opencv_utils.h
 * @brief OpenCV 操作工具类
 * <AUTHOR> Calibration Team
 * @date 2024
 */

#ifndef CAMERA_CALIBRATION_UTILS_OPENCV_UTILS_H
#define CAMERA_CALIBRATION_UTILS_OPENCV_UTILS_H

#include <opencv2/opencv.hpp>
#include <string>
#include <vector>

namespace camera_calibration {
namespace utils {

/**
 * @brief OpenCV 操作工具类
 * 
 * 提供常用的 OpenCV 操作封装，减少重复代码
 */
class OpenCVUtils {
public:
    /**
     * @brief 从数组创建相机内参矩阵
     * @param mtx 3x3 相机内参数组
     * @return OpenCV 相机矩阵
     */
    static cv::Mat createCameraMatrix(double mtx[3][3]);
    
    /**
     * @brief 从数组创建畸变系数矩阵
     * @param dist 畸变系数数组
     * @param size 数组大小 (默认8)
     * @return OpenCV 畸变系数矩阵
     */
    static cv::Mat createDistortionCoeffs(double dist[], int size = 8);
    
    /**
     * @brief 安全保存图像，带错误处理
     * @param image 待保存的图像
     * @param path 保存路径
     * @param class_name 调用类名 (用于日志)
     * @return 保存是否成功
     */
    static bool saveImageSafely(const cv::Mat& image, 
                               const std::string& path,
                               const std::string& class_name = "OpenCVUtils");
    
    /**
     * @brief 安全读取图像，带错误处理
     * @param path 图像路径
     * @param image 输出图像
     * @param flags 读取标志 (默认彩色)
     * @param class_name 调用类名 (用于日志)
     * @return 读取是否成功
     */
    static bool loadImageSafely(const std::string& path,
                               cv::Mat& image,
                               int flags = cv::IMREAD_COLOR,
                               const std::string& class_name = "OpenCVUtils");
    
    /**
     * @brief 验证图像是否为白色像素
     * @param pixel_value 像素值
     * @return 是否为白色
     */
    static bool isWhitePixel(uint8_t pixel_value);
    // static bool isWhitePixel(cv::Vec3b &pixel);
    
    /**
     * @brief 验证图像是否为黑色像素
     * @param pixel_value 像素值
     * @return 是否为黑色
     */
    static bool isBlackPixel(uint8_t pixel_value);
    
    /**
     * @brief 创建标准的角点检测参数
     * @return TermCriteria 对象
     */
    static cv::TermCriteria createCornerSubPixCriteria();
    
    /**
     * @brief 安全执行 OpenCV 操作，带异常处理
     * @param operation 要执行的操作
     * @param operation_name 操作名称 (用于日志)
     * @param class_name 调用类名 (用于日志)
     * @return 操作是否成功
     */
    template<typename Func>
    static bool executeOpenCVOperation(Func&& operation,
                                      const std::string& operation_name,
                                      const std::string& class_name = "OpenCVUtils");
    
    /**
     * @brief 安全执行 OpenCV 操作，带异常处理和返回值
     * @param operation 要执行的操作
     * @param default_value 默认返回值
     * @param operation_name 操作名称 (用于日志)
     * @param class_name 调用类名 (用于日志)
     * @return 操作结果或默认值
     */
    template<typename Func, typename ReturnType>
    static ReturnType executeOpenCVOperation(Func&& operation,
                                            ReturnType default_value,
                                            const std::string& operation_name,
                                            const std::string& class_name = "OpenCVUtils");
    
    /**
     * @brief 验证矩阵是否有效
     * @param matrix 待验证的矩阵
     * @param expected_rows 期望的行数 (-1 表示不检查)
     * @param expected_cols 期望的列数 (-1 表示不检查)
     * @param class_name 调用类名 (用于日志)
     * @return 矩阵是否有效
     */
    static bool validateMatrix(const cv::Mat& matrix,
                              int expected_rows = -1,
                              int expected_cols = -1,
                              const std::string& class_name = "OpenCVUtils");
    
    /**
     * @brief 创建标准的形态学核
     * @param kernel_size 核大小
     * @param shape 核形状 (默认矩形)
     * @return 形态学核
     */
    static cv::Mat createMorphologyKernel(int kernel_size,
                                         int shape = cv::MORPH_RECT);

    /**
     * @brief 计算两点之间的欧几里得距离
     * @param p1 第一个点
     * @param p2 第二个点
     * @return 距离
     */
    static double calculateDistance(const cv::Point2f& p1, const cv::Point2f& p2);

private:
    // 禁止实例化
    OpenCVUtils() = delete;
    ~OpenCVUtils() = delete;
    OpenCVUtils(const OpenCVUtils&) = delete;
    OpenCVUtils& operator=(const OpenCVUtils&) = delete;
};

} // namespace utils
} // namespace camera_calibration

// 模板函数实现
#include "opencv_utils_impl.h"

#endif // CAMERA_CALIBRATION_UTILS_OPENCV_UTILS_H
