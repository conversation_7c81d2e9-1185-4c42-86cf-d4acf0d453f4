#ifndef CAMERA_CALIBRATION_CORE_TYPES_H
#define CAMERA_CALIBRATION_CORE_TYPES_H

#include <opencv2/opencv.hpp>
#include <vector>
#include <string>
#include <memory>

namespace camera_calibration {
namespace core {

/**
 * @brief 2D 点信息结构
 */
struct Point2D {
    double x;
    double y;
    
    Point2D() : x(0.0), y(0.0) {}
    Point2D(double x_val, double y_val) : x(x_val), y(y_val) {}
    
    // 转换为 OpenCV Point2f
    cv::Point2f toCvPoint() const {
        return cv::Point2f(static_cast<float>(x), static_cast<float>(y));
    }
    
    // 从 OpenCV Point2f 创建
    static Point2D fromCvPoint(const cv::Point2f& pt) {
        return Point2D(pt.x, pt.y);
    }
};

/**
 * @brief 3D 点信息结构
 */
struct Point3D {
    double x;
    double y;
    double z;
    
    Point3D() : x(0.0), y(0.0), z(0.0) {}
    Point3D(double x_val, double y_val, double z_val) : x(x_val), y(y_val), z(z_val) {}
    
    // 转换为 OpenCV Point3f
    cv::Point3f toCvPoint() const {
        return cv::Point3f(static_cast<float>(x), static_cast<float>(y), static_cast<float>(z));
    }
    
    // 从 OpenCV Point3f 创建
    static Point3D fromCvPoint(const cv::Point3f& pt) {
        return Point3D(pt.x, pt.y, pt.z);
    }
};

/**
 * @brief 相机内参结构
 */
struct CameraIntrinsics {
    double fx;          // x 方向焦距
    double fy;          // y 方向焦距
    double cx;          // x 方向主点
    double cy;          // y 方向主点
    
    CameraIntrinsics() : fx(0.0), fy(0.0), cx(0.0), cy(0.0) {}
    CameraIntrinsics(double fx_val, double fy_val, double cx_val, double cy_val)
        : fx(fx_val), fy(fy_val), cx(cx_val), cy(cy_val) {}
    
    // 转换为 OpenCV 相机矩阵
    cv::Mat toCameraMatrix() const {
        cv::Mat camera_matrix = cv::Mat::eye(3, 3, CV_64F);
        camera_matrix.at<double>(0, 0) = fx;
        camera_matrix.at<double>(1, 1) = fy;
        camera_matrix.at<double>(0, 2) = cx;
        camera_matrix.at<double>(1, 2) = cy;
        return camera_matrix;
    }
};

/**
 * @brief 畸变系数结构
 */
struct DistortionCoefficients {
    double k1, k2, k3;     // 径向畸变系数
    double p1, p2;         // 切向畸变系数
    double k4, k5, k6;     // 高阶径向畸变系数（可选）
    
    DistortionCoefficients() : k1(0.0), k2(0.0), k3(0.0), p1(0.0), p2(0.0), k4(0.0), k5(0.0), k6(0.0) {}
    
    // 转换为 OpenCV 畸变系数向量
    cv::Mat toDistCoeffs() const {
        cv::Mat dist_coeffs = cv::Mat::zeros(8, 1, CV_64F);
        dist_coeffs.at<double>(0) = k1;
        dist_coeffs.at<double>(1) = k2;
        dist_coeffs.at<double>(2) = p1;
        dist_coeffs.at<double>(3) = p2;
        dist_coeffs.at<double>(4) = k3;
        dist_coeffs.at<double>(5) = k4;
        dist_coeffs.at<double>(6) = k5;
        dist_coeffs.at<double>(7) = k6;
        return dist_coeffs;
    }
    
    // 从数组创建
    static DistortionCoefficients fromArray(const double* coeffs, size_t size) {
        DistortionCoefficients dist;
        if (size >= 1) dist.k1 = coeffs[0];
        if (size >= 2) dist.k2 = coeffs[1];
        if (size >= 3) dist.p1 = coeffs[2];
        if (size >= 4) dist.p2 = coeffs[3];
        if (size >= 5) dist.k3 = coeffs[4];
        if (size >= 6) dist.k4 = coeffs[5];
        if (size >= 7) dist.k5 = coeffs[6];
        if (size >= 8) dist.k6 = coeffs[7];
        return dist;
    }
};

/**
 * @brief 标定结果结构
 */
struct CalibrationResult {
    Point3D world_position;    // 世界坐标位置
    Point2D pixel_position;    // 像素坐标位置
    Point2D left_top;          // 左上角点
    Point2D right_top;         // 右上角点
    Point2D right_bottom;      // 右下角点
    Point2D left_bottom;       // 左下角点
    double confidence;         // 置信度
    
    CalibrationResult() : confidence(0.0) {}
};

/**
 * @brief 特征点信息结构
 */
struct FeaturePoint {
    Point2D pixel_coord;       // 像素坐标
    cv::Vec2d polar_coord;     // 极坐标 (距离, 角度)
    cv::Point label;           // 标签 (列, 行)
    double area;               // 面积
    double circularity;        // 圆度
    
    FeaturePoint() : area(0.0), circularity(0.0) {}
};

/**
 * @brief 图像处理参数结构
 */
struct ImageProcessingParams {
    // 图像尺寸
    int src_width;
    int src_height;
    int output_width;
    int output_height;
    
    // 棋盘格参数
    int chess_rows;
    int chess_cols;
    cv::Rect chess_bounds;
    
    // 处理参数
    int bottom_threshold;
    int chess_y_offset;
    int dark_threshold;
    int darken_amount;
    std::vector<double> msrcr_weights;
    std::vector<double> msrcr_sigmas;
    double msrcr_gain;
    double msrcr_offset;

    std::vector<int> morphology_kernel_size;
    int morphology_dilate_iterations;
    int morphology_erode_iterations;

    int border_width;
    int border_bottom_height;
    
    // 世界坐标范围
    double world_x_min, world_x_max;
    double world_y_min, world_y_max;

    // 图像处理范围
    cv::Range img_x_range;
    cv::Range img_y_range;

    // 列点数验证
    std::vector<int> left_column_counts;
    std::vector<int> right_column_counts;
    
    ImageProcessingParams()
        : src_width(1280), src_height(720)
        , output_width(1280), output_height(720)
        , chess_rows(0), chess_cols(0)
        , bottom_threshold(20), chess_y_offset(37)
        , dark_threshold(165), darken_amount(50)
        , world_x_min(0), world_x_max(82)
        , world_y_min(-125), world_y_max(125)
        , img_x_range(30, 1250), img_y_range(400, 720) {}
};

/**
 * @brief Blob 检测参数结构
 */
struct BlobDetectorParams {
    bool filter_by_color;
    int blob_color;
    
    bool filter_by_area;
    float min_area;
    float max_area;
    
    bool filter_by_circularity;
    float min_circularity;
    float max_circularity;
    
    bool filter_by_convexity;
    float min_convexity;
    float max_convexity;
    
    bool filter_by_inertia;
    float min_inertia_ratio;
    float max_inertia_ratio;
    
    BlobDetectorParams()
        : filter_by_color(true), blob_color(0)
        , filter_by_area(true), min_area(200.0f), max_area(100000.0f)
        , filter_by_circularity(true), min_circularity(0.05f), max_circularity(0.99f)
        , filter_by_convexity(false), min_convexity(0.87f), max_convexity(1.0f)
        , filter_by_inertia(false), min_inertia_ratio(0.05f), max_inertia_ratio(0.99f) {}
    
    // 转换为 OpenCV SimpleBlobDetector::Params
    cv::SimpleBlobDetector::Params toOpenCVParams() const;
};

/**
 * @brief 错误码枚举
 */
enum class ErrorCode {
    SUCCESS = 0,
    FILE_NOT_FOUND = -1,
    INVALID_CONFIG = -2,
    IMAGE_LOAD_FAILED = -3,
    CALIBRATION_FAILED = -4,
    FORWARD_CALIB_FAILED = -5,
    FEATURE_DETECTION_FAILED = -6,
    INSUFFICIENT_POINTS = -7,
    INVALID_PARAMETERS = -8,
    FEATURE_LABELING_FAILED = -9,
    POINT_COUNT_VALIDATION_FAILED = -10,
    TABLE_GENERATION_FAILED = -11,
    GENERAL_ERROR = -12
};

/**
 * @brief 将错误码转换为字符串
 */
std::string errorCodeToString(ErrorCode code);

} // namespace core
} // namespace camera_calibration

#endif // CAMERA_CALIBRATION_CORE_TYPES_H
