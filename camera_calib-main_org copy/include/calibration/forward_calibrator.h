#ifndef CAMERA_CALIBRATION_CALIBRATION_FORWARD_CALIBRATOR_H
#define CAMERA_CALIBRATION_CALIBRATION_FORWARD_CALIBRATOR_H

#include "core/types.h"
#include <opencv2/opencv.hpp>
#include <opencv2/features2d.hpp>
#include <string>
#include <vector>
#include <fstream>

namespace camera_calibration {
namespace calibration {

/**
 * @brief 前向标定器类
 * 
 * 负责执行基于棋盘格的前向相机标定，包括：
 * - 棋盘格角点检测
 * - 图像去畸变
 * - 透视变换
 * - 标定图像生成
 */
class ForwardCalibrator {
public:
    /**
     * @brief 构造函数
     */
    ForwardCalibrator();
    
    /**
     * @brief 析构函数
     */
    ~ForwardCalibrator();
    
    /**
     * @brief 初始化标定器
     * @param intrinsics 相机内参
     * @param dist_coeffs 畸变系数
     * @param processing_params 图像处理参数
     * @return 初始化是否成功
     */
    bool initialize(const core::CameraIntrinsics& intrinsics,
                   const core::DistortionCoefficients& dist_coeffs,
                   const core::ImageProcessingParams& processing_params);
    
    /**
     * @brief 执行前向棋盘格标定
     * @param input_image_path 输入图像路径
     * @param output_path 输出路径
     * @param calibrated_image 输出标定后的图像
     * @return 标定结果错误码
     */
    core::ErrorCode performCalibration(const std::string& input_image_path,
                                      const std::string& output_path, const std::string& debug_path,
                                      cv::Mat& calibrated_image);


    
    /**
     * @brief 检测棋盘格角点 (对应原项目的 detectChessboard)
     * @param image 输入图像
     * @param corners 输出角点坐标
     * @return 检测是否成功
     */
    bool detectChessboard(const cv::Mat& image, std::vector<cv::Point2f>& corners, const std::string& debug_path);

    /**
     * @brief 计算棋盘格透视变换矩阵 (对应原项目的 getChessWarpMatrix)
     * @param corners 输入的棋盘格角点
     * @param warpM 输出的透视变换矩阵
     * @return 计算是否成功
     */
    bool getChessWarpMatrix(const std::vector<cv::Point2f>& corners, cv::Mat& warpM, const std::string& output_path);

    /**
     * @brief 检测盲区底部位置 (对应原项目的 getBottom)
     * @param calibed_image 校正后的图像
     * @return 盲区底部位置，失败返回 -1
     */
    int getBottom(const cv::Mat& calibed_image);

    /**
     * @brief 计算反向透视变换矩阵 (对应原项目的 getChessInvHomoMatrix)
     * @param corners 棋盘格角点
     * @param bottom 盲区底部位置
     * @param inv_homo 输出的反向透视变换矩阵
     * @return 计算是否成功
     */
    bool getChessInvHomoMatrix(const std::vector<cv::Point2f>& corners, int bottom, cv::Mat& inv_homo);

    /**
     * @brief 图像后处理 (对应原项目的 postProcess)
     * @param image 输入输出图像
     */
    void postProcess(cv::Mat& image);


    

    
    /**
     * @brief 获取标定统计信息
     */
    struct CalibrationStats {
        bool corners_detected;
        int corners_count;
        double corner_detection_accuracy;
        cv::Size image_size;
        double reprojection_error;
        std::string status_message;
    };
    
    CalibrationStats getLastCalibrationStats() const { return last_stats_; }
    

    
    /**
     * @brief 保存调试图像
     * @param image 图像
     * @param filename 文件名
     * @param output_path 输出路径
     */
    void saveDebugImage(const cv::Mat& image, const std::string& filename, 
                       const std::string& output_path);
    
    /**
     * @brief 设置调试模式
     * @param enable 是否启用调试模式
     */
    void setDebugMode(bool enable) { debug_mode_ = enable; }
    
    /**
     * @brief 获取调试模式状态
     */
    bool isDebugMode() const { return debug_mode_; }

    // 特征点信息结构体 (对应原项目的 PointInfo)
    struct PointInfo {
        cv::Point coor;     // 像素坐标
        cv::Vec2d poly;     // 极坐标 (距离, 角度)
        cv::Point label;    // 标签 (列号, 行号)
    };



    /**
     * @brief 标记特征点 (对应原项目的 LabelthePoint)
     * @param keypoints 检测到的关键点
     * @param result_set 输出的标记结果
     * @param reference_point 参考点
     * @param left_num 左侧点数
     * @param right_num 右侧点数
     * @return 处理是否成功
     */
    bool labelPoints(const std::vector<cv::Point>& keypoints,
                    std::vector<PointInfo>& result_set,
                    cv::Point& reference_point,
                    int& left_num, int& right_num);

    /**
     * @brief 计算两点间距离
     * @param point1 第一个点
     * @param point2 第二个点
     * @return 距离
     */
    double getDistance(const cv::Point& point1, const cv::Point& point2);



    /**
     * @brief 写入CSV文件 (对应原项目的 write_csv)
     * @param points 点信息
     * @param filename 文件名
     * @return 写入是否成功
     */
    bool writeCSV(const std::vector<PointInfo>& points, const std::string& filename);



    /**
     * @brief 初始化标定映射表 (对应原项目的 initCalibMap)
     * @param mtx 相机内参矩阵
     * @param dist 畸变参数
     * @param HomoI 反向透视变换矩阵
     * @param mapx 输出X映射
     * @param mapy 输出Y映射
     * @param height 图像高度
     * @param width 图像宽度
     * @param outSize 输出尺寸
     * @param ratio 缩放比例
     */
    void initCalibMap(double mtx[3][3], double dist[8], const cv::Mat& HomoI,
                     cv::Mat& mapx, cv::Mat& mapy, int height, int width,
                     cv::Size outSize = cv::Size(1280, 720), int ratio = 2);
    
        /**
     * @brief 将Mat数据写入二进制文件
     * @param filename 文件名
     * @param _M 要写入的Mat数据
     * @return 写入是否成功
     */
    bool matWrite(std::string filename, cv::Mat& _M);

    /**
     * @brief 前向棋盘格标定 (对应原项目的 forwardChessCalibration)
     * @param imgpath 输入图像路径
     * @param outfile 输出文件路径
     * @param mtx 相机内参矩阵
     * @param dist 畸变参数
     * @param outImg 输出标定图像
     * @return 标定结果，0表示成功，-1表示失败
     */
    int forwardChessCalibration(const char* imgpath, const std::string& output_path, const std::string& debug_path,
                               double mtx[3][3], double dist[8],
                               cv::Mat& outImg);

    // 重构后的子函数声明
    /**
     * @brief 加载和验证输入图像
     * @param imgpath 图像路径
     * @param srcImg 输出的源图像
     * @param outfile 输出目录
     * @return 是否成功
     */
    bool loadAndValidateImage(const char* imgpath, cv::Mat& srcImg, const std::string& output_path);

    /**
     * @brief 执行去畸变处理
     * @param srcImg 源图像
     * @param undistortImg 去畸变后的图像
     * @param mtx 相机内参矩阵
     * @param dist 畸变参数
     * @param outfile 输出目录
     * @return 是否成功
     */
    bool performUndistortion(const cv::Mat& srcImg, cv::Mat& undistortImg,
                            double mtx[3][3], double dist[8], const std::string& output_path);

    /**
     * @brief 检测和处理棋盘格
     * @param undistortImg 去畸变图像
     * @param corners 检测到的角点
     * @return 是否成功
     */
    bool detectAndProcessChessboard(const cv::Mat& undistortImg, std::vector<cv::Point2f>& corners, const std::string& debug_path);

    /**
     * @brief 执行透视变换
     * @param undistortImg 去畸变图像
     * @param warpImg 透视变换后的图像
     * @param corners 角点
     * @param warpM 透视变换矩阵
     * @param outfile 输出目录
     * @return 是否成功
     */
    bool performPerspectiveTransform(const cv::Mat& undistortImg, cv::Mat& warpImg,
                                   const std::vector<cv::Point2f>& corners,
                                   cv::Mat& warpM, const std::string& outfile, const std::string& debug_path);

    /**
     * @brief 检测盲区
     * @param warpImg 透视变换后的图像
     * @param bottom 盲区底部位置
     * @return 是否成功
     */
    bool detectBlindArea(const cv::Mat& warpImg, int& bottom);

    /**
     * @brief 生成标定映射
     * @param corners 角点
     * @param bottom 盲区底部
     * @param mtx 相机内参矩阵
     * @param dist 畸变参数
     * @param Mapx X方向映射
     * @param Mapy Y方向映射
     * @return 是否成功
     */
    bool generateCalibrationMaps(const std::vector<cv::Point2f>& corners, int bottom,
                               double mtx[3][3], double dist[8],
                               cv::Mat& Mapx, cv::Mat& Mapy, const std::string& output_path);

    /**
     * @brief 应用最终标定
     * @param srcImg 源图像
     * @param outImg 输出图像
     * @param Mapx X方向映射
     * @param Mapy Y方向映射
     * @param outfile 输出目录
     * @return 是否成功
     */
    bool applyFinalCalibration(const cv::Mat& srcImg, cv::Mat& outImg,
                             const cv::Mat& Mapx, const cv::Mat& Mapy,
                             const std::string& outfile);
    
private:
    // 计算角点的亚像素精度
    void refineCorners(const cv::Mat& image, std::vector<cv::Point2f>& corners);
    


    // 图像后处理
    void postProcessImage(cv::Mat& image);
    
    // 注意：日志记录使用统一的LOG_*_FUNC宏
    
    // 成员变量
    bool initialized_;
    bool debug_mode_;
    
    // 相机参数
    core::CameraIntrinsics camera_intrinsics_;
    core::DistortionCoefficients distortion_coeffs_;
    core::ImageProcessingParams processing_params_;
    
    // OpenCV 相机矩阵和畸变系数
    cv::Mat camera_matrix_;
    cv::Mat dist_coeffs_mat_;
    
    // 去畸变映射表
    cv::Mat map_x_;
    cv::Mat map_y_;
    bool maps_initialized_;
    
    // 棋盘格参数
    cv::Size chessboard_size_;
    cv::Rect chessboard_bounds_;
    
    // 统计信息
    CalibrationStats last_stats_;
    
    // 常量
    static const double CORNER_SUB_PIX_WINDOW_SIZE;
    static const int CORNER_SUB_PIX_MAX_ITERATIONS;
    static const double CORNER_SUB_PIX_EPSILON;
    static const double MAX_REPROJECTION_ERROR_THRESHOLD;
};

} // namespace calibration
} // namespace camera_calibration

#endif // CAMERA_CALIBRATION_CALIBRATION_FORWARD_CALIBRATOR_H
