#ifndef CAMERA_CALIBRATION_PROCESSING_COORDINATE_MAPPER_H
#define CAMERA_CALIBRATION_PROCESSING_COORDINATE_MAPPER_H

#include "core/types.h"
#include <opencv2/opencv.hpp>
#include <vector>
#include <string>

namespace camera_calibration {
namespace processing {

/**
 * @brief 坐标映射器类
 * 
 * 负责像素坐标到世界坐标的映射，包括：
 * - 标定数据加载和管理
 * - 三角形插值算法
 * - 最近邻插值算法
 * - 查找表生成
 */
class CoordinateMapper {
public:
    /**
     * @brief 构造函数
     */
    CoordinateMapper();
    
    /**
     * @brief 析构函数
     */
    ~CoordinateMapper();
    
    /**
     * @brief 初始化坐标映射器
     * @param processing_params 图像处理参数
     * @return 初始化是否成功
     */
    bool initialize(const core::ImageProcessingParams& processing_params);
    
    
    /**
     * @brief 将像素坐标映射到世界坐标
     * @param pixel_point 像素坐标点
     * @return 标定结果
     */
    core::CalibrationResult mapPixelToWorld(const core::Point2D& pixel_point);
    
    
    /**
     * @brief 保存标定结果
     * @param feature_points 特征点列表
     * @param output_file 输出文件路径
     * @return 保存是否成功
     */
    bool saveCalibrationResults(const std::vector<core::FeaturePoint>& feature_points,
                               const std::string& output_file);
    
    /**
     * @brief 设置调试模式
     * @param enable 是否启用调试模式
     */
    void setDebugMode(bool enable) { debug_mode_ = enable; }
    
    /**
     * @brief 获取调试模式状态
     */
    bool isDebugMode() const { return debug_mode_; }

private:
    /**
     * @brief 点信息结构（兼容原项目）
     */
    struct PointInfo {
        double x;
        double y;
        PointInfo() : x(0.0), y(0.0) {}
        PointInfo(double x_val, double y_val) : x(x_val), y(y_val) {}
    };
    
    // 初始化方法
    void initializeWorldGrid();
    void initializePixelGrid();
    
    // 插值算法
    bool isPointInTriangle(const core::Point2D& point,
                          const PointInfo& p1, const PointInfo& p2, const PointInfo& p3);
    
    core::Point3D triangularInterpolation(const core::Point2D& point,
                                         const PointInfo& p1, const PointInfo& p2, const PointInfo& p3,
                                         const PointInfo& w1, const PointInfo& w2, const PointInfo& w3);
    
    core::CalibrationResult nearestNeighborInterpolation(const core::Point2D& pixel_point);
    
    // 几何计算辅助函数
    double crossProduct(const PointInfo& p1, const PointInfo& p2, const core::Point2D& p3);
    double calculateTriangleArea(const PointInfo& p1, const PointInfo& p2, const core::Point2D& p3);

    // 查找表生成相关的辅助方法（与原项目完全一致）
    bool generateLookupTableWithInterpolation(float* loc, int table_height, int table_width,
                                             int IMGX_RANGE[2], int IMGY_RANGE[2],
                                             std::ofstream& file, std::ofstream& log);
    int findNonZero3Original(float* loc_temp, int pixel_row, int table_width, int IMGX_RANGE[2], int IMGY_RANGE[2]);
    int findNonZero4Original(float* loc_temp, int pixel_row, int table_width, int IMGX_RANGE[2], int IMGY_RANGE[2]);
    bool checkColumnConnectivityOriginal(float* loc_temp, int row, int col1, int col2, int table_width, int IMGX_RANGE[2], int IMGY_RANGE[2]);
    int calcLocOriginal(double loc, int s_min, int s_max);


    
    // 成员变量
    bool initialized_;
    bool debug_mode_;
    
    // 处理参数
    core::ImageProcessingParams processing_params_;
    
    // 网格参数
    int cols_;
    int rows_;
    double distance_to_camera_;
    double distance_to_camera_center_;
    
    // 坐标网格
    std::vector<std::vector<PointInfo>> pixels_3d_;  // 像素坐标网格
    std::vector<std::vector<PointInfo>> poses_3d_;   // 世界坐标网格
    
    // 世界坐标轴
    std::vector<double> h_axis_;  // Z 方向（深度）
    std::vector<double> w_axis_;  // Y 方向（横向）
};

} // namespace processing
} // namespace camera_calibration

#endif // CAMERA_CALIBRATION_PROCESSING_COORDINATE_MAPPER_H
