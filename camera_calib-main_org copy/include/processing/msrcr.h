#ifndef CAMERA_CALIBRATION_PROCESSING_MSRCR_H
#define CAMERA_CALIBRATION_PROCESSING_MSRCR_H

#include <opencv2/opencv.hpp>
#include <opencv2/imgproc/imgproc_c.h>
#include <vector>

namespace camera_calibration {
namespace processing {

/**
 * @brief Multi-Scale Retinex with Color Restoration (MSRCR) 图像增强类
 *
 * 实现与原项目 Msrcr 类完全一致的 MSRCR 算法，用于图像增强处理
 * 对应原项目的 msrcr.MultiScaleRetinexCR(src1, src_enhance, weight, sigema, 128, 128)
 */
class Msrcr {

public:
    /**
     * @brief 多尺度 Retinex 带颜色恢复算法 (完全对应原项目的 MultiScaleRetinexCR)
     * @param src 输入图像
     * @param dst 输出图像
     * @param weights 权重向量
     * @param sigmas 标准偏差向量
     * @param gain 增益 (默认128)
     * @param offset 偏移 (默认128)
     * @param restoration_factor 颜色恢复因子 (默认6.0)
     * @param color_gain 颜色增益 (默认2.0)
     */
    void MultiScaleRetinexCR(cv::Mat src, cv::Mat &dst,
                            std::vector<double> weights,
                            std::vector<double> sigmas,
                            int gain = 128, int offset = 128,
                            double restoration_factor = 6.0, double color_gain = 2.0);

    /**
     * @brief 多尺度 Retinex 带颜色恢复算法 IplImage 版本 (对应原项目的核心实现)
     * @param img 输入输出图像指针
     * @param weights 权重向量
     * @param sigmas 标准偏差向量
     * @param gain 增益 (默认128)
     * @param offset 偏移 (默认128)
     * @param restoration_factor 颜色恢复因子 (默认6.0)
     * @param color_gain 颜色增益 (默认2.0)
     */
    void MultiScaleRetinexCR(IplImage *img, std::vector<double> weights, std::vector<double> sigmas,
                            int gain = 128, int offset = 128,
                            double restoration_factor = 6.0, double color_gain = 2.0);

private:
    /**
     * @brief 快速滤波 (对应原项目的 FastFilter)
     * @param img 输入输出图像指针
     * @param sigma 标准偏差
     */
    void FastFilter(IplImage *img, double sigma);

    /**
     * @brief 快速滤波 Mat 版本
     * @param src 输入图像
     * @param dst 输出图像
     * @param sigma 标准偏差
     */
    void FastFilter(cv::Mat src, cv::Mat &dst, double sigma);
};

} // namespace processing
} // namespace camera_calibration

#endif // CAMERA_CALIBRATION_PROCESSING_MSRCR_H
